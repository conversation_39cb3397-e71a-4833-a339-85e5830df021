import type { APIRoute } from 'astro';
import { nanoid } from 'nanoid';

export const prerender = false;

export const POST: APIRoute = async ({ locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: "Database not configured." 
      }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Create a test user first
    const testUserEmail = '<EMAIL>';
    const testUserId = 'test-user-id-123';

    // Check if user exists, if not create one
    const existingUser = await db.prepare(`
      SELECT id FROM users WHERE email = ?
    `).bind(testUserEmail).first();

    if (!existingUser) {
      await db.prepare(`
        INSERT INTO users (id, email, name, created_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
      `).bind(testUserId, testUserEmail, 'Test User').run();
    }

    // Sample custom domains to insert
    const sampleDomains = [
      {
        domain: 'example.com',
        status: 'active',
        verified: 1
      },
      {
        domain: 'mydomain.net',
        status: 'pending',
        verified: 0
      },
      {
        domain: 'custom.org',
        status: 'active',
        verified: 1
      }
    ];

    const insertedDomains = [];

    for (const domainData of sampleDomains) {
      const domainId = nanoid();
      
      // Check if domain already exists
      const existingDomain = await db.prepare(`
        SELECT id FROM custom_domains WHERE domain = ?
      `).bind(domainData.domain).first();

      if (!existingDomain) {
        await db.prepare(`
          INSERT INTO custom_domains (
            id,
            user_id,
            domain,
            status,
            ssl_status,
            verification_method,
            pages_project_name,
            verified,
            created_at,
            updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `).bind(
          domainId,
          testUserId,
          domainData.domain,
          domainData.status,
          'active',
          'dns',
          'qr-redirect-backend-v2',
          domainData.verified
        ).run();

        insertedDomains.push({
          id: domainId,
          domain: domainData.domain,
          status: domainData.status,
          verified: domainData.verified === 1
        });
      }
    }

    return new Response(JSON.stringify({
      success: true,
      message: `Inserted ${insertedDomains.length} test domains`,
      domains: insertedDomains,
      test_user_id: testUserId,
      test_user_email: testUserEmail
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error creating test domains:', error);
    return new Response(JSON.stringify({
      success: false,
      error: (error as Error).message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
